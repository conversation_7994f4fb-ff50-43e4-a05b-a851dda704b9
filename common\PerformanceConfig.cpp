#include "PerformanceConfig.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

namespace PerformanceConfig {

    // Current active profile with default values
    static PerformanceProfile g_currentProfile = {
        60,     // jpegQuality
        60,     // frameRateLimit
        6,      // compressionLevel
        TRUE,   // useHardwareAccel
        TRUE,   // adaptiveQuality
        "Default"
    };

    BOOL LoadConfigFromFile(const char* filename, PerformanceProfile* profile)
    {
        if (!filename || !profile) return FALSE;

        FILE* file = fopen(filename, "r");
        if (!file) return FALSE;

        // Set default values
        profile->jpegQuality = 60;
        profile->frameRateLimit = 60;
        profile->compressionLevel = 6;
        profile->useHardwareAccel = TRUE;
        profile->adaptiveQuality = TRUE;
        strcpy(profile->profileName, "Loaded");

        char line[256];
        while (fgets(line, sizeof(line), file)) {
            // Skip comments and empty lines
            if (line[0] == '#' || line[0] == '\n' || line[0] == '\r') continue;

            char key[64], value[64];
            if (sscanf(line, "%63[^=]=%63s", key, value) == 2) {
                if (strcmp(key, "jpeg_quality") == 0) {
                    profile->jpegQuality = atoi(value);
                } else if (strcmp(key, "frame_rate") == 0) {
                    profile->frameRateLimit = atoi(value);
                } else if (strcmp(key, "compression_level") == 0) {
                    profile->compressionLevel = atoi(value);
                } else if (strcmp(key, "hardware_accel") == 0) {
                    profile->useHardwareAccel = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "adaptive_quality") == 0) {
                    profile->adaptiveQuality = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "profile_name") == 0) {
                    strncpy(profile->profileName, value, sizeof(profile->profileName) - 1);
                    profile->profileName[sizeof(profile->profileName) - 1] = '\0';
                }
            }
        }

        fclose(file);
        return TRUE;
    }

    void ApplyPerformanceProfile(const PerformanceProfile* profile)
    {
        if (!profile) return;
        g_currentProfile = *profile;
    }

    void GetCurrentProfile(PerformanceProfile* profile)
    {
        if (profile) {
            *profile = g_currentProfile;
        }
    }

}