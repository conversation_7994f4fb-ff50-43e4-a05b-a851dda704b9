#pragma once
#include <Windows.h>

// Simple performance configuration
namespace PerformanceConfig {
    // Basic performance settings structure
    struct PerformanceProfile {
        ULONG jpegQuality;      // 1-100
        DWORD frameRateLimit;   // FPS limit
        DWORD compressionLevel; // 1-9
        BOOL useHardwareAccel;  // Hardware acceleration
        BOOL adaptiveQuality;   // Adaptive quality adjustment
        char profileName[64];
    };

    // Configuration file functions
    BOOL LoadConfigFromFile(const char* filename, PerformanceProfile* profile);
    void ApplyPerformanceProfile(const PerformanceProfile* profile);
    void GetCurrentProfile(PerformanceProfile* profile);
}
