@echo off
REM Build script for HVNC.sln with multiple performance optimizations

echo ========================================
echo HVNC Multi-Configuration Build System
echo ========================================
echo.

REM Try to find MSBuild in common locations
set "MSBUILD_PATH="

REM VS2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2022 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

if "%MSBUILD_PATH%"=="" (
    echo MSBuild.exe not found. Please install Visual Studio or Build Tools and update the path in this script.
    exit /b 1
)

REM Create build directories
if not exist "build" mkdir "build"
if not exist "build\gaming" mkdir "build\gaming"
if not exist "build\office" mkdir "build\office"
if not exist "build\design" mkdir "build\design"

REM Backup original performance config
if exist "hvnc_performance.ini" copy "hvnc_performance.ini" "hvnc_performance_backup.ini" >nul

echo Building Gaming Configuration (Speed Optimized)...
call :create_gaming_config
"%MSBUILD_PATH%" HVNC.sln /p:Configuration=Release /p:OutDir=build\gaming\
if errorlevel 1 (
    echo Gaming build failed.
    goto :cleanup
)
echo Gaming build completed successfully.
echo.

echo Building Office Configuration (Balanced)...
call :create_office_config
"%MSBUILD_PATH%" HVNC.sln /p:Configuration=Release /p:OutDir=build\office\
if errorlevel 1 (
    echo Office build failed.
    goto :cleanup
)
echo Office build completed successfully.
echo.

echo Building Design Configuration (Quality Optimized)...
call :create_design_config
"%MSBUILD_PATH%" HVNC.sln /p:Configuration=Release /p:OutDir=build\design\
if errorlevel 1 (
    echo Design build failed.
    goto :cleanup
)
echo Design build completed successfully.
echo.

:cleanup
REM Restore original config
if exist "hvnc_performance_backup.ini" (
    copy "hvnc_performance_backup.ini" "hvnc_performance.ini" >nul
    del "hvnc_performance_backup.ini" >nul
)

echo ========================================
echo Build Summary
echo ========================================
echo Gaming build (Speed):   build\gaming\
echo Office build (Balanced): build\office\
echo Design build (Quality):  build\design\
echo.
echo Each build is optimized for different use cases:
echo - Gaming: Low latency, high FPS, reduced quality
echo - Office: Balanced performance and quality
echo - Design: High quality, lower FPS for detailed work
echo.
echo To use a specific build, copy the executables from the desired folder.
goto :eof

:create_gaming_config
(
echo # HVNC Performance Configuration - Gaming Build
echo # Optimized for maximum speed and responsiveness
echo.
echo # Image Quality Settings
echo jpeg_quality=40
echo frame_rate=45
echo compression_level=4
echo hardware_accel=true
echo adaptive_quality=true
echo.
echo profile_name=Gaming
) > hvnc_performance.ini
goto :eof

:create_office_config
(
echo # HVNC Performance Configuration - Office Build
echo # Balanced performance and quality
echo.
echo # Image Quality Settings
echo jpeg_quality=60
echo frame_rate=60
echo compression_level=6
echo hardware_accel=true
echo adaptive_quality=true
echo.
echo profile_name=Office
) > hvnc_performance.ini
goto :eof

:create_design_config
(
echo # HVNC Performance Configuration - Design Build
echo # Optimized for best image quality
echo.
echo # Image Quality Settings
echo jpeg_quality=85
echo frame_rate=30
echo compression_level=8
echo hardware_accel=false
echo adaptive_quality=false
echo.
echo profile_name=Design
) > hvnc_performance.ini
goto :eof
